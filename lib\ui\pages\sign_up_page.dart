import 'package:espot/models/user_model.dart';
import 'package:espot/shared/constant.dart';
import 'package:espot/shared/snackbar.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/ui/widgets/buttons.dart';
import 'package:espot/ui/widgets/forms.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final nameController = TextEditingController(text: '');
  final emailController = TextEditingController(text: '');
  final phoneNumberController = TextEditingController(text: '');
  final passwordController = TextEditingController(text: '');

  // Tambahkan variabel untuk kontrol visibilitas password
  bool _isPasswordVisible = false;

  bool isEmailValid(String email) {
    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(email);
  }

  bool isPasswordStrong(String password) {
    return password.length >= 8 &&
        password.contains(RegExp(r'[A-Z]')) &&
        password.contains(RegExp(r'[a-z]')) &&
        password.contains(RegExp(r'[0-9]')) &&
        password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
  }

  bool isPhoneNumberValid(String phoneNumber) {
    final phoneRegex = RegExp(r'^(\+62|62|0)8[1-9][0-9]{7,11}$');
    return phoneRegex.hasMatch(phoneNumber);
  }

  bool validate() {
    if (nameController.text.isEmpty) {
      CustomSnackBar.showToast(context, 'Nama tidak boleh kosong');
      return false;
    }

    if (!isEmailValid(emailController.text)) {
      CustomSnackBar.showToast(context, 'Format email tidak valid');
      return false;
    }

    if (!isPhoneNumberValid(phoneNumberController.text)) {
      CustomSnackBar.showToast(context, 'Nomor telepon tidak valid');
      return false;
    }

    if (!isPasswordStrong(passwordController.text)) {
      CustomSnackBar.showToast(context,
          'Password harus minimal 8 karakter, mengandung huruf besar, huruf kecil, angka, dan karakter spesial');
      return false;
    }

    return true;
  }

  Future<void> addUser(
      String uid, String name, String email, String phone) async {
    DatabaseReference ref =
        FirebaseDatabase.instance.ref().child(USERS).child(uid);
    return ref.set({
      'name': name,
      'email': email,
      'phone': phone,
      'profilePicture': '',
      'verified': false,
    }).then((value) {
      print('User added successfully.');
    }).catchError((error) {
      print('Failed to add user: $error');
    });
  }

  Future<UserModel?> signUpWithEmailAndPassword(
      String name, String email, String password, String phone) async {
    try {
      UserCredential userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);

      User? user = userCredential.user;
      if (user != null) {
        UserModel userModel = UserModel(
          uid: user.uid,
          name: name,
          email: email,
          phone: phone,
          profilePicture: '',
          verified: false,
        );
        await addUser(user.uid, name, email, phone);
        return userModel;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      print('Failed with error code: ${e.code}');
      print(e.message);
      
      // Show specific error message to user
      String errorMessage = 'Gagal mendaftar: ';
      if (e.code == 'email-already-in-use') {
        errorMessage += 'Email sudah terdaftar';
      } else if (e.code == 'weak-password') {
        errorMessage += 'Password terlalu lemah';
      } else if (e.code == 'invalid-email') {
        errorMessage += 'Format email tidak valid';
      } else {
        errorMessage += e.message ?? 'Terjadi kesalahan';
      }
      
      CustomSnackBar.showToast(context, errorMessage);
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView(
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
        ),
        children: [
          Container(
            width: 180,
            height: 180,
            margin: const EdgeInsets.only(
              top: 100,
              bottom: 50,
            ),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/img_logo_light.png',
                ),
              ),
            ),
          ),
          Text(
            'Join Us to Espot',
            style: blackTextStyle.copyWith(
              fontSize: 20,
              fontWeight: semiBold,
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          Container(
            padding: const EdgeInsets.all(22),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: whiteColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // NOTE: NAME INPUT
                CustomFormField(
                  title: 'Full Name',
                  controller: nameController,
                ),
                const SizedBox(
                  height: 16,
                ),
                CustomFormField(
                  title: 'Email',
                  controller: emailController,
                ),
                const SizedBox(
                  height: 16,
                ),
                // NOTE: PHONE INPUT
                CustomFormField(
                  title: 'Phone Number',
                  controller: phoneNumberController,
                ),
                const SizedBox(
                  height: 16,
                ),
                // NOTE: PASSWORD INPUT
                CustomFormField(
                  title: 'Password',
                  obscureText: !_isPasswordVisible,
                  controller: passwordController,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isPasswordVisible
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: greyColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
                CustomFilledButton(
                  title: 'Continue',
                  onPressed: () async {
                    // Tutup keyboard
                    FocusScope.of(context).unfocus();

                    try {
                      if (validate()) {
                        EasyLoading.show(
                            status: 'loading...'); // Pindah ke sini
                        final result = await signUpWithEmailAndPassword(
                          nameController.text,
                          emailController.text,
                          passwordController.text,
                          phoneNumberController.text,
                        );

                        if (result != null) {
                          EasyLoading.dismiss();
                          Navigator.pushReplacementNamed(
                              context, '/sign-up-success');
                        } else {
                          EasyLoading.dismiss();
                          CustomSnackBar.showToast(
                            context,
                            'Gagal mendaftar, silakan coba lagi',
                          );
                        }
                      } else {
                        CustomSnackBar.showToast(
                          context,
                          'Input masih kosong',
                        );
                      }
                    } catch (e) {
                      EasyLoading.dismiss();
                      CustomSnackBar.showToast(
                        context,
                        'Terjadi kesalahan: ${e.toString()}',
                      );
                    }
                  },
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          CustomTextButton(
            title: 'Sign In',
            onPressed: () {
              Navigator.pushNamed(context, '/sign-in');
            },
          ),
          const SizedBox(
            height: 30,
          ),
        ],
      ),
    );
  }
}

