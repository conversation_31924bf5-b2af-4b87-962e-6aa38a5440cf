/**
 * Model Tim - Representasi data Tim dalam aplikasi ESPOT
 * 
 * File ini mendefinisikan struktur data untuk tim yang disimpan di Firebase.
 * TeamsModel digunakan untuk mengelola informasi tim termasuk deskripsi,
 * daftar pemain, dan status verifikasi.
 */

/**
 * TeamsModel - Model data untuk Tim
 * 
 * Props:
 * - uid       : String? - ID unik untuk tim
 * - desc      : String? - Deskripsi atau nama tim
 * - player1-5 : String? - ID atau nama pemain dalam tim (maksimal 5 pemain)
 * - image     : String? - URL atau path ke logo tim
 * - verified  : int?    - Status verifikasi tim (0: belum diverifikasi, 1: terverifikasi)
 */
class TeamsModel {
  String? uid;
  String? desc;
  String? player1;
  String? player2;
  String? player3;
  String? player4;
  String? player5;
  String? image;
  int? verified;

  /**
   * Constructor
   * 
   * Menciptakan objek model tim dengan semua parameter opsional
   */
  TeamsModel({
    this.uid,
    this.desc,
    this.player1,
    this.player2,
    this.player3,
    this.player4,
    this.player5,
    this.image,
    this.verified,
  });

  /**
   * Factory method: Mengkonversi data dari Firebase menjadi model
   * @param data - Map data dari Firebase
   * @param uid  - ID unik untuk tim
   * @return     - Instance baru dari TeamsModel
   */
  factory TeamsModel.fromMap(Map<dynamic, dynamic> data, String uid) {
    return TeamsModel(
      uid: uid,
      desc: data['desc'] as String?,
      player1: data['player1'] as String?,
      player2: data['player2'] as String?,
      player3: data['player3'] as String?,
      player4: data['player4'] as String?,
      player5: data['player5'] as String?,
      image: data['image'] as String?,
      verified: data['verified'] as int?,
    );
  }
}
