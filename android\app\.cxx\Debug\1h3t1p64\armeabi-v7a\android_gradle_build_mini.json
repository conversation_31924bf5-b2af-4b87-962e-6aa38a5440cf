{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["c:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\kampus\\nazril\\espot\\android\\app\\.cxx\\Debug\\1h3t1p64\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["c:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\kampus\\nazril\\espot\\android\\app\\.cxx\\Debug\\1h3t1p64\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}