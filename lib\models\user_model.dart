/**
 * Model Pengguna
 * Representasi data Pengguna dalam aplikasi ESPOT
 * 
 * File ini mendefinisikan struktur data untuk pengguna yang disimpan di Firebase.
 * UserModel digunakan untuk mengelola informasi pengguna seperti nama, kontak,
 * status verifikasi, dan foto profil.
 */

/**
 * UserModel - Model data untuk Pengguna
 * 
 * Props: 
 * - uid           : String? - ID unik untuk pengguna
 * - name          : String? - <PERSON>a pengguna
 * - phone         : String? - Nomor telepon pengguna
 * - email         : String? - Alamat email pengguna
 * - password      : String? - Kata sandi pengguna (tidak disimpan di database)
 * - verified      : bool?   - Status verifikasi pengguna
 * - profilePicture: String? - URL atau path ke foto profil pengguna
 */
class UserModel {
  String? uid;
  String? name;
  String? phone;
  String? email;
  String? password;
  bool? verified;
  String? profilePicture;

  /**
   * Constructor
   * Menciptakan objek model pengguna dengan semua parameter opsional
   */
  UserModel({
    this.uid,
    this.name,
    this.phone,
    this.email,
    this.password,
    this.verified,
    this.profilePicture,
  });

  /**
   * Factory method: Mengkonversi data dari Firebase menjadi model
   * @param data - Map data dari Firebase
   * @param uid  - ID unik untuk pengguna
   * @return     - Instance baru dari UserModel
   */
  factory UserModel.fromMap(Map<dynamic, dynamic> data, String uid) {
    return UserModel(
      uid: uid,
      name: data['name'] as String?,
      phone: data['phone'] as String?,
      email: data['email'] as String?,
      verified: data['verified'] as bool?,
      profilePicture: data['profilePicture'] as String?,
    );
  }
}
