/**
 * Model Formulir Edit Pengguna
 * Representasi data untuk pengeditan profil pengguna dalam aplikasi ESPOT
 * 
 * File ini mendefinisikan struktur data untuk informasi yang dimasukkan
 * saat pengguna mengedit profil mereka. Model ini digunakan untuk memproses
 * data dari formulir edit profil sebelum disimpan ke database.
 */

class UserEditFormModel {
  final String? name; // Nama pengguna yang diperbarui
  final String? phoneNumber; // Nomor telepon yang diperbarui
  final String? password; // Kata sandi yang diperbarui

  /**
   * Constructor
   * Menciptakan objek model formulir edit dengan semua field opsional
   */
  UserEditFormModel({
    this.name,
    this.phoneNumber,
    this.password,
  });

  /**
   * toJson: Mengkonversi model menjadi format JSON
   * 
   * Metode ini mengkonversi data model ke Map yang dapat dikirim ke API
   * atau disimpan ke database dengan nama field yang sesuai format backend
   * 
   * @return Map<String, dynamic> - Representasi JSON dari model
   */
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone_number': phoneNumber,
      'password': password,
    };
  }
}
