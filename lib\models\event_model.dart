/**
 * Model Event
 * Representasi data Event dalam aplikasi ESPOT
 * 
 * File ini mendefinisikan struktur data untuk event yang disimpan di Firebase.
 * EventModel digunakan untuk mengelola dan menyimpan informasi event seperti
 * deskripsi dan gambar.
 */

/**
 * EventModel - Model data untuk Event
 * 
 * Props:
 * - uid   : String? - ID unik untuk event
 * - desc  : String? - Deskripsi event
 * - image : String? - URL atau path ke gambar event
 */
class EventModel {
  String? uid;
  String? desc;
  String? image;

  /**
   * Constructor
   * Menciptakan objek EventModel baru dengan parameter opsional
   */
  EventModel({
    this.uid,
    this.desc,
    this.image,
  });

  /**
   * Factory method: Mengkonversi data dari Firebase menjadi model
   * @param data - Map data dari Firebase
   * @param uid  - ID unik untuk event
   * @return     - Instance baru dari EventModel
   */
  factory EventModel.fromMap(Map<dynamic, dynamic> data, String uid) {
    return EventModel(
      uid: uid,
      desc: data['desc'] as String?,
      image: data['image'] as String?,
    );
  }
}
