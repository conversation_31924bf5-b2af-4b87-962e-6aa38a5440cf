/**
 * Model Formulir Pendaftaran
 * Representasi data untuk pendaftaran pengguna dalam aplikasi ESPOT
 * 
 * File ini mendefinisikan struktur data untuk informasi yang dimasukkan
 * pengguna saat mendaftar. Model ini digunakan untuk memproses data dari
 * formulir pendaftaran sebelum disimpan ke database.
 */

class SignUpFormModel {
  final String? name; // Nama pengguna
  final String? phoneNumber; // Nomor telepon pengguna
  final String? password; // Kata sandi pengguna
  final String? profilePicture; // URL/path foto profil

  /**
   * Constructor
   * Menciptakan objek model formulir pendaftaran dengan semua field opsional
   */
  SignUpFormModel({
    this.name,
    this.phoneNumber,
    this.password,
    this.profilePicture,
  });

  /**
   * toJson: Mengkonversi model menjadi format JSON
   * Metode ini mengkonversi data model ke Map yang dapat dikirim ke API
   * atau disimpan ke database dengan nama field yang sesuai format backend
   * 
   * @return Map<String, dynamic> - Representasi JSON dari model
   */
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone_number': phoneNumber,
      'password': password,
      'profile_picture': profilePicture,
    };
  }

  /**
   * copyWith: Membuat salinan model dengan beberapa nilai yang diperbarui
   * 
   * Metode ini memungkinkan pembaruan sebagian dari model tanpa
   * mengubah nilai yang tidak perlu diperbarui
   * 
   * @return SignUpFormModel - Model baru dengan nilai yang diperbarui
   */
  SignUpFormModel copyWith({
    String? name,
    String? phoneNumber,
    String? password,
    String? profilePicture,
  }) =>
      SignUpFormModel(
        name: name ?? this.name,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        password: password ?? this.password,
        profilePicture: profilePicture ?? this.profilePicture,
      );
}
